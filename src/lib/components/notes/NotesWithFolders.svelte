<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { goto } from '$app/navigation';
	import { WEBUI_NAME, config, models, settings } from '$lib/stores';

	import { createNewNote } from '$lib/apis/notes';
	import {
		getNotesInFolder,
		getNotesInRoot,
		getNotesInShared,
		getNoteFolderPath,
		getNoteFolderTree,
		getSharedNoteFolderTree
	} from '$lib/apis/note-folders';
	import { getTimeRange } from '$lib/utils';

	import { PaneGroup, Pane, PaneResizer } from 'paneforge';

	import EnhancedFolderTree from './EnhancedFolderTree.svelte';
	import EnhancedNoteList from './EnhancedNoteList.svelte';
	import Search from '../icons/Search.svelte';
	import Plus from '../icons/Plus.svelte';
	import XMark from '../icons/XMark.svelte';
	import ChevronRight from '../icons/ChevronRight.svelte';

	import ChatBubbleOval from '../icons/ChatBubbleOval.svelte';
	import Tooltip from '../common/Tooltip.svelte';
	import NotePanel from './NotePanel.svelte';
	import Chat from './NoteEditor/Chat.svelte';

	const i18n = getContext('i18n');

	let selectedFolderId: string | null = null;
	let selectedRootType: 'root' | 'shared' = 'root';
	let notes: any = {};
	let filteredNotes: any = {};
	let query = '';
	let loaded = false;
	let folderPath: any[] = [];

	// 增強功能相關狀態
	let selectedNotes: Set<string> = new Set();
	let folders: any[] = [];
	let selectedNote: any = null;

	// AI Chat 相關變數
	let showPanel = false;
	let selectedPanel = 'chat';
	let selectedModelId = null;
	let messages = [];
	let editing = false;
	let streaming = false;
	let stopResponseFlag = false;

	// 初始化
	const init = async () => {
		await Promise.all([loadNotes(), loadFolders()]);
	};

	// 載入資料夾列表
	const loadFolders = async () => {
		try {
			const [ownedFolders, sharedFolders] = await Promise.all([
				getNoteFolderTree(localStorage.token),
				getSharedNoteFolderTree(localStorage.token)
			]);

			// 扁平化資料夾列表供批量操作使用
			const flattenFolders = (folderList: any[], prefix = '') => {
				let result: any[] = [];
				folderList.forEach((folder) => {
					result.push({
						...folder,
						displayName: prefix + folder.name
					});
					if (folder.children && folder.children.length > 0) {
						result = result.concat(flattenFolders(folder.children, prefix + folder.name + ' / '));
					}
				});
				return result;
			};

			folders = [...flattenFolders(ownedFolders), ...flattenFolders(sharedFolders, '共用 / ')];
		} catch (error) {
			console.error('載入資料夾失敗:', error);
		}
	};

	// 載入筆記
	const loadNotes = async () => {
		try {
			let notesList = [];

			if (selectedFolderId === null) {
				if (selectedRootType === 'root') {
					// 載入根目錄筆記
					notesList = await getNotesInRoot(localStorage.token);
				} else {
					// 載入共用區域筆記
					notesList = await getNotesInShared(localStorage.token);
				}
				// 清空資料夾路徑
				folderPath = [];
			} else {
				// 載入指定資料夾筆記
				notesList = await getNotesInFolder(localStorage.token, selectedFolderId);
				// 載入資料夾路徑
				folderPath = await getNoteFolderPath(localStorage.token, selectedFolderId);
			}

			// 按時間分組
			const groupedNotes = {};
			notesList.forEach((note) => {
				const timeRange = getTimeRange(note.updated_at / 1000000000);
				if (!groupedNotes[timeRange]) {
					groupedNotes[timeRange] = [];
				}
				groupedNotes[timeRange].push(note);
			});

			notes = groupedNotes;
			applyFilter();
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 應用搜索過濾
	const applyFilter = () => {
		if (!query.trim()) {
			filteredNotes = notes;
			return;
		}

		const filtered = {};
		Object.keys(notes).forEach((timeRange) => {
			const filteredTimeRangeNotes = notes[timeRange].filter(
				(note) =>
					note.title.toLowerCase().includes(query.toLowerCase()) ||
					(note.data?.content?.md || '').toLowerCase().includes(query.toLowerCase())
			);
			if (filteredTimeRangeNotes.length > 0) {
				filtered[timeRange] = filteredTimeRangeNotes;
			}
		});
		filteredNotes = filtered;
	};

	// 資料夾選擇處理
	const handleFolderSelected = async (event) => {
		selectedFolderId = event.detail.folderId;
		if (event.detail.rootType) {
			selectedRootType = event.detail.rootType;
		}
		// 清除筆記選擇
		selectedNotes.clear();
		selectedNotes = selectedNotes;
		await loadNotes();
	};

	// 處理筆記移動完成
	const handleNotesMoved = async (event) => {
		// 重新載入筆記列表
		await loadNotes();
		// 清除選擇
		selectedNotes.clear();
		selectedNotes = selectedNotes;
	};

	// 處理筆記選擇
	const handleNoteSelected = (event) => {
		selectedNote = event.detail.note;
		// 如果不是多選模式，導航到筆記頁面
		if (!event.detail.multiSelectMode) {
			goto(`/notes/${selectedNote.id}`);
		}
	};

	// 創建新筆記
	const createNoteHandler = async () => {
		try {
			const res = await createNewNote(localStorage.token, {
				title: new Date().toISOString().split('T')[0], // YYYY-MM-DD
				data: {
					content: {
						json: null,
						html: '',
						md: ''
					}
				},
				meta: null,
				access_control: {},
				folder_id: selectedFolderId
			});

			if (res) {
				goto(`/notes/${res.id}`);
			}
		} catch (error) {
			toast.error(`${error}`);
		}
	};

	// 監聽搜索查詢變化
	$: if (query !== undefined) {
		applyFilter();
	}

	// 初始化模型選擇
	const initModel = () => {
		if ($settings?.models) {
			selectedModelId = $settings?.models[0];
		} else if ($config?.default_models) {
			selectedModelId = $config?.default_models.split(',')[0];
		} else {
			selectedModelId = '';
		}

		if (selectedModelId) {
			const model = $models
				.filter((model) => model.id === selectedModelId && !(model?.info?.meta?.hidden ?? false))
				.find((model) => model.id === selectedModelId);

			if (!model) {
				selectedModelId = '';
			}
		}

		if (!selectedModelId) {
			selectedModelId = $models.at(0)?.id || '';
		}
	};

	// 創建虛擬筆記對象供 Chat 組件使用
	const createVirtualNote = () => {
		const notesList = Object.values(filteredNotes).flat();
		const notesContent = notesList
			.map((note) => `## ${note.title}\n${note.data?.content?.md || ''}`)
			.join('\n\n');

		return {
			id: 'virtual-notes',
			title: selectedFolderId ? `資料夾筆記` : '所有筆記',
			data: {
				content: {
					md: notesContent,
					html: '',
					json: null
				},
				files: null
			}
		};
	};

	onMount(async () => {
		await init();
		initModel();
		loaded = true;
	});
</script>

<svelte:head>
	<title>
		{$i18n.t('Notes')} • {$WEBUI_NAME}
	</title>
</svelte:head>

<div id="note-container" class="w-full h-full">
	<PaneGroup direction="horizontal" class="w-full h-full">
		<!-- 左側資料夾樹 -->
		<Pane defaultSize={30} minSize={15} maxSize={50} class="h-full">
			<div class="w-full h-full border-r border-gray-200 dark:border-gray-700">
				<EnhancedFolderTree
					bind:selectedFolderId
					bind:selectedRootType
					bind:selectedNotes
					on:folderSelected={handleFolderSelected}
					on:notesMoved={handleNotesMoved}
				/>
			</div>
		</Pane>

		<!-- 分隔線 -->
		<PaneResizer
			class="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors cursor-col-resize"
		/>

		<!-- 右側筆記列表 -->
		<Pane defaultSize={70} minSize={30} class="h-full flex flex-col w-full relative">
			<div class="w-full h-full overflow-hidden flex flex-col">
				<!-- 頭部工具欄 -->
				<div
					class="flex flex-col gap-1 px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex-shrink-0"
				>
					<!-- 面包屑導航 -->
					{#if folderPath.length > 0}
						<div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
							<button
								type="button"
								class="hover:text-gray-900 dark:hover:text-gray-200"
								on:click={() =>
									handleFolderSelected({
										detail: { folderId: null, rootType: selectedRootType }
									})}
							>
								{selectedRootType === 'root' ? $i18n.t('Root') : $i18n.t('Shared')}
							</button>
							{#each folderPath as folder, index}
								<ChevronRight className="w-4 h-4 mx-1" />
								<button
									type="button"
									class="hover:text-gray-900 dark:hover:text-gray-200 {index ===
									folderPath.length - 1
										? 'font-medium text-gray-900 dark:text-gray-100'
										: ''}"
									on:click={() => handleFolderSelected({ detail: { folderId: folder.id } })}
								>
									{folder.name}
								</button>
							{/each}
						</div>
					{/if}

					<!-- 搜索和新建按鈕 -->
					<div class="flex items-center space-x-3">
						<div class="flex-1 flex items-center">
							<div class="mr-3">
								<Search className="w-4 h-4" />
							</div>
							<input
								class="w-full text-sm py-1 bg-transparent outline-none"
								bind:value={query}
								placeholder={$i18n.t('Search Notes')}
							/>
							{#if query}
								<button
									class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
									on:click={() => {
										query = '';
									}}
								>
									<XMark className="w-3 h-3" />
								</button>
							{/if}
						</div>
						<button
							type="button"
							class="flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
							on:click={createNoteHandler}
						>
							<Plus className="w-4 h-4 mr-1" />
							{$i18n.t('New Note')}
						</button>
						<Tooltip
							placement="top"
							content={$i18n.t('Chat with Notes')}
							className="cursor-pointer"
						>
							<button
								type="button"
								class="flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
								on:click={() => {
									if (showPanel && selectedPanel === 'chat') {
										showPanel = false;
									} else {
										if (!showPanel) {
											showPanel = true;
										}
										selectedPanel = 'chat';
									}
								}}
							>
								<ChatBubbleOval className="w-4 h-4 mr-1" />
								{$i18n.t('Chat')}
							</button>
						</Tooltip>
					</div>
				</div>

				<!-- 增強筆記列表 -->
				<div class="flex-1 overflow-hidden">
					<EnhancedNoteList
						{filteredNotes}
						{selectedNote}
						{loaded}
						{folders}
						on:noteSelected={handleNoteSelected}
						on:notesChanged={handleNotesMoved}
					/>
				</div>
			</div>
		</Pane>

		<!-- NotePanel for AI Chat -->
		<NotePanel bind:show={showPanel}>
			{#if selectedPanel === 'chat'}
				<Chat
					bind:show={showPanel}
					bind:selectedModelId
					bind:messages
					note={createVirtualNote()}
					bind:editing
					bind:streaming
					bind:stopResponseFlag
					editor={null}
					inputElement={null}
					selectedContent={null}
					files={[]}
					onInsert={() => {}}
					onStop={() => {
						stopResponseFlag = true;
					}}
					onEdited={() => {}}
					insertNoteHandler={() => {}}
					scrollToBottomHandler={() => {}}
				/>
			{/if}
		</NotePanel>
	</PaneGroup>
</div>
